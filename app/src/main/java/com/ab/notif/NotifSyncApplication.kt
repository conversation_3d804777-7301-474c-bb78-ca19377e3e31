package com.ab.notif

import android.app.Application
import android.content.Intent
import android.util.Log
import androidx.work.*
import com.ab.notif.firebase.RemoteConfigManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit

/**
 * Application class that initializes WorkManager and starts periodic location tracking.
 * This ensures that location capture begins as soon as the app is installed and runs.
 * Phase 4: Added Firebase Remote Config for dynamic location intervals and notification filtering.
 */
class NotifSyncApplication : Application() {

    companion object {
        private const val TAG = "NOTIF_SYNC_APP"
        private const val LOG_PREFIX = "[APPLICATION]"
    }

    private val remoteConfigManager = RemoteConfigManager.getInstance()
    private val applicationScope = CoroutineScope(Dispatchers.IO)

    override fun onCreate() {
        super.onCreate()
        Log.i(TAG, "$LOG_PREFIX NotifSyncApplication started - Phase 4 with Remote Config")

        // Initialize Remote Config and location tracking
        applicationScope.launch {
            initializeRemoteConfig()
        }

        initializeLocationTracking()
        startLocationService()
    }

    private fun initializeLocationTracking() {
        try {
            // Get initial location interval from Remote Config (or default)
            val locationIntervalMinutes = remoteConfigManager.getLocationIntervalMinutes()

            scheduleLocationWork(locationIntervalMinutes)

            // Register listener for location interval changes
            remoteConfigManager.addLocationIntervalListener { newInterval ->
                Log.i(TAG, "$LOG_PREFIX Location interval changed to $newInterval minutes, rescheduling work")
                scheduleLocationWork(newInterval)
            }

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error initializing location tracking: ${e.message}", e)
        }
    }

    private fun scheduleLocationWork(intervalMinutes: Long) {
        try {
            // Create constraints for the work
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .setRequiresBatteryNotLow(false)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .setRequiresStorageNotLow(false)
                .build()

            // Create periodic work request with dynamic interval
            val locationWorkRequest = PeriodicWorkRequestBuilder<LocationWorker>(
                intervalMinutes, TimeUnit.MINUTES,
                5, TimeUnit.MINUTES // Flex interval for battery optimization
            )
                .setConstraints(constraints)
                .setBackoffCriteria(
                    BackoffPolicy.LINEAR,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()

            // Enqueue the work with replace policy to update interval
            WorkManager.getInstance(this).enqueueUniquePeriodicWork(
                LocationWorker.WORK_NAME,
                ExistingPeriodicWorkPolicy.REPLACE, // Replace existing work to update interval
                locationWorkRequest
            )

            Log.i(TAG, "$LOG_PREFIX Location tracking WorkManager scheduled - capturing every $intervalMinutes minutes")

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error scheduling location work: ${e.message}", e)
        }
    }

    private fun startLocationService() {
        try {
            // Start the foreground service for background location access
            val serviceIntent = Intent(this, LocationService::class.java)
            startForegroundService(serviceIntent)
            
            Log.i(TAG, "$LOG_PREFIX LocationService started")
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error starting LocationService: ${e.message}", e)
        }
    }

    private suspend fun initializeRemoteConfig() {
        try {
            Log.d(TAG, "$LOG_PREFIX Initializing Remote Config...")

            // Initialize Remote Config
            val initialized = remoteConfigManager.initialize(this)
            if (!initialized) {
                Log.w(TAG, "$LOG_PREFIX Remote Config initialization failed, using default values")
                return
            }

            // Fetch initial config
            val fetched = remoteConfigManager.fetchAndActivate(this)
            if (fetched) {
                Log.i(TAG, "$LOG_PREFIX Initial Remote Config fetch successful")
            } else {
                Log.d(TAG, "$LOG_PREFIX Using cached or default Remote Config values")
            }

            // Schedule periodic config refresh every 12 hours
            scheduleConfigRefresh()

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error initializing Remote Config: ${e.message}", e)
        }
    }

    private fun scheduleConfigRefresh() {
        try {
            // Create constraints for config refresh work
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED) // Need network for config fetch
                .setRequiresBatteryNotLow(false)
                .setRequiresCharging(false)
                .setRequiresDeviceIdle(false)
                .setRequiresStorageNotLow(false)
                .build()

            // Create periodic work request for config refresh every 12 hours
            val configRefreshRequest = PeriodicWorkRequestBuilder<ConfigRefreshWorker>(
                12, TimeUnit.HOURS,
                1, TimeUnit.HOURS // Flex interval
            )
                .setConstraints(constraints)
                .setBackoffCriteria(
                    BackoffPolicy.EXPONENTIAL,
                    WorkRequest.MIN_BACKOFF_MILLIS,
                    TimeUnit.MILLISECONDS
                )
                .build()

            // Enqueue the work
            WorkManager.getInstance(this).enqueueUniquePeriodicWork(
                "config_refresh_work",
                ExistingPeriodicWorkPolicy.KEEP,
                configRefreshRequest
            )

            Log.i(TAG, "$LOG_PREFIX Config refresh scheduled - checking every 12 hours")

        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Error scheduling config refresh: ${e.message}", e)
        }
    }
}
