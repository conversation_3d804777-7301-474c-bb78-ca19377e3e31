package com.ab.notif.firebase

import android.content.Context
import android.util.Log
import com.ab.notif.data.LocationData
import com.ab.notif.data.NotificationData
import com.ab.notif.utils.DeviceIdManager
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreSettings
import kotlinx.coroutines.tasks.await

/**
 * Firebase integration manager for syncing notifications and locations to Firestore.
 * Handles authentication, offline sync, and error handling.
 */
class FirebaseManager private constructor() {
    
    companion object {
        private const val TAG = "FIREBASE_MANAGER"
        private const val LOG_PREFIX = "[FIREBASE]"
        
        // Firestore collection paths
        private const val COLLECTION_DEVICES = "devices"
        private const val COLLECTION_NOTIFICATIONS = "notifications"
        private const val COLLECTION_LOCATIONS = "locations"
        
        @Volatile
        private var INSTANCE: FirebaseManager? = null
        
        fun getInstance(): FirebaseManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: FirebaseManager().also { INSTANCE = it }
            }
        }
    }
    
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val firestore: FirebaseFirestore = FirebaseFirestore.getInstance()
    private var isInitialized = false
    
    /**
     * Initializes Firebase with offline persistence and anonymous authentication
     */
    suspend fun initialize(context: Context): Boolean {
        return try {
            if (isInitialized) {
                Log.d(TAG, "$LOG_PREFIX Firebase already initialized")
                return true
            }
            
            // Enable offline persistence
            val settings = FirebaseFirestoreSettings.Builder()
                .setPersistenceEnabled(true)
                .build()
            firestore.firestoreSettings = settings
            
            // Authenticate anonymously
            if (auth.currentUser == null) {
                Log.d(TAG, "$LOG_PREFIX Authenticating anonymously...")
                auth.signInAnonymously().await()
                Log.i(TAG, "$LOG_PREFIX Anonymous authentication successful")
            } else {
                Log.d(TAG, "$LOG_PREFIX Already authenticated: ${auth.currentUser?.uid}")
            }
            
            isInitialized = true
            Log.i(TAG, "$LOG_PREFIX Firebase initialized successfully")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to initialize Firebase: ${e.message}", e)
            false
        }
    }
    
    /**
     * Syncs notification data to Firestore
     */
    suspend fun syncNotification(context: Context, notificationData: NotificationData): SyncResult {
        return try {
            if (!isInitialized) {
                Log.w(TAG, "$LOG_PREFIX Firebase not initialized, attempting to initialize...")
                if (!initialize(context)) {
                    return SyncResult.FAILED
                }
            }
            
            val deviceId = DeviceIdManager.getDeviceId(context)
            val dataWithDeviceId = notificationData.copy(deviceId = deviceId)
            
            firestore
                .collection(COLLECTION_DEVICES)
                .document(deviceId)
                .collection(COLLECTION_NOTIFICATIONS)
                .add(dataWithDeviceId)
                .await()
            
            Log.d(TAG, "$LOG_PREFIX Notification synced successfully for device: $deviceId")
            SyncResult.SYNCED
            
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to sync notification: ${e.message}", e)
            SyncResult.FAILED
        }
    }
    
    /**
     * Syncs location data to Firestore
     */
    suspend fun syncLocation(context: Context, locationData: LocationData): SyncResult {
        return try {
            if (!isInitialized) {
                Log.w(TAG, "$LOG_PREFIX Firebase not initialized, attempting to initialize...")
                if (!initialize(context)) {
                    return SyncResult.FAILED
                }
            }
            
            val deviceId = DeviceIdManager.getDeviceId(context)
            val dataWithDeviceId = locationData.copy(deviceId = deviceId)
            
            firestore
                .collection(COLLECTION_DEVICES)
                .document(deviceId)
                .collection(COLLECTION_LOCATIONS)
                .add(dataWithDeviceId)
                .await()
            
            Log.d(TAG, "$LOG_PREFIX Location synced successfully for device: $deviceId")
            SyncResult.SYNCED
            
        } catch (e: Exception) {
            Log.e(TAG, "$LOG_PREFIX Failed to sync location: ${e.message}", e)
            SyncResult.FAILED
        }
    }
    
    /**
     * Checks if Firebase is properly initialized and connected
     */
    fun isReady(): Boolean {
        return isInitialized && auth.currentUser != null
    }
    
    /**
     * Gets the current device ID
     */
    fun getDeviceId(context: Context): String {
        return DeviceIdManager.getDeviceId(context)
    }
}

/**
 * Enum representing sync operation results
 */
enum class SyncResult {
    SYNCED,
    FAILED
}
