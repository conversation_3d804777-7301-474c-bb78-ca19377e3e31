**Task: Android App Phase 5 - Add Location Push on Notification Receipt**

**Objective:**
Extend the existing app to capture and push current location to Firebase whenever ANY notification is received (regardless of filtering). Keep all existing functionality intact.

**Requirements:**
1. **Keep ALL existing functionality** - notification capture, periodic location tracking, Firebase sync, Remote Config, and logging
2. **Add instant location capture** - get current location immediately when any notification arrives
3. **Push to Firebase** - save this instant location to the same locations collection
4. **No notification content needed** - just use the notification event as a trigger for location capture
5. **Separate from periodic location** - this is additional to the 15-minute interval location tracking

**Technical Specifications:**
- Trigger location capture in NotificationListenerService's `onNotificationPosted()` method
- Use FusedLocationProviderClient for instant location request
- Save to the same Firebase `locations/` collection with additional metadata
- Continue all existing periodic location tracking unchanged
- Apply notification package filtering (don't trigger location for excluded packages)

**Enhanced Location Data Structure:**
```
locations/
└── {auto-generated-doc-id}/
    ├── timestamp: long
    ├── latitude: double
    ├── longitude: double
    ├── accuracy: float
    ├── device_id: string
    ├── trigger_type: string  // "periodic" or "notification"
    └── trigger_package: string (optional) // package name when triggered by notification
```

**Implementation Requirements:**
1. Add location request in NotificationListenerService
2. Distinguish between periodic vs notification-triggered locations
3. Handle location permission and availability
4. Respect notification package filtering from Remote Config
5. Add location request timeout (5 seconds max)
6. Handle rapid notifications (debounce if multiple notifications within 30 seconds)

**Enhanced Logging Format:**
```
[NOTIFICATION] 2024-01-15 14:30:25 | WhatsApp | John Doe | New message received | SYNCED
[LOCATION-INSTANT] 2024-01-15 14:30:26 | Lat: -6.2088, Lng: 106.8456 | Accuracy: 8.5m | Trigger: com.whatsapp | SYNCED
[NOTIFICATION-FILTERED] 2024-01-15 14:30:30 | com.android.systemui | System UI | EXCLUDED | NO_LOCATION
[LOCATION-PERIODIC] 2024-01-15 14:45:30 | Lat: -6.2090, Lng: 106.8460 | Accuracy: 12.5m | Scheduled | SYNCED
```

**Key Implementation Points:**
1. **Debouncing**: If multiple notifications arrive within 30 seconds, only capture location for the first one
2. **Error Handling**: If location request fails/times out, log error but continue processing notification
3. **Filtering Respect**: Don't capture location for notifications from excluded packages
4. **Performance**: Use high accuracy but with 5-second timeout to avoid blocking

**Additional Components Needed:**
1. Instant location request logic in NotificationListenerService
2. Location request timeout handling
3. Debouncing mechanism for rapid notifications
4. Enhanced Firebase data model with trigger information
5. Location availability checking

**Important Notes:**
- Don't break any existing functionality
- This is ADDITIONAL to periodic location tracking (both should work simultaneously)
- Handle location service unavailability gracefully
- Log all instant location attempts (success/failure)
- Respect all existing Remote Config settings

**Deliverables:**
1. Updated NotificationListenerService with instant location capture
2. Enhanced location data structure with trigger information
3. Debouncing mechanism for rapid notifications
4. All existing functionality preserved and working
5. Enhanced logging showing both periodic and notification-triggered locations
